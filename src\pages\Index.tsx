
import { useState } from "react";
import { Plus, Search, Filter, Users, CheckSquare, FileText } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import Sidebar from "@/components/Sidebar";
import AddLeadDialog from "@/components/AddLeadDialog";
import AddTaskDialog from "@/components/AddTaskDialog";
import LeadCard from "@/components/LeadCard";
import TaskCard from "@/components/TaskCard";

interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  status: 'new' | 'contacted' | 'in-progress' | 'closed';
  tags: string[];
  createdAt: string;
  assignedTo: string;
  value: number;
}

interface Task {
  id: string;
  title: string;
  description: string;
  leadId: string;
  leadName: string;
  assignedTo: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  dueDate: string;
  createdAt: string;
}

const mockLeads: Lead[] = [
  {
    id: '1',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'TechCorp Solutions',
    status: 'new',
    tags: ['enterprise', 'priority'],
    createdAt: '2025-01-15',
    assignedTo: 'John Doe',
    value: 15000
  },
  {
    id: '2',
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Innovate Co',
    status: 'contacted',
    tags: ['startup', 'tech'],
    createdAt: '2025-01-14',
    assignedTo: 'Jane Smith',
    value: 8500
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Growth.io',
    status: 'in-progress',
    tags: ['saas', 'recurring'],
    createdAt: '2025-01-13',
    assignedTo: 'John Doe',
    value: 25000
  }
];

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Follow up call with Sarah',
    description: 'Schedule product demo for next week',
    leadId: '1',
    leadName: 'Sarah Johnson',
    assignedTo: 'John Doe',
    priority: 'high',
    status: 'pending',
    dueDate: '2025-01-18',
    createdAt: '2025-01-15'
  },
  {
    id: '2',
    title: 'Send proposal to Michael',
    description: 'Prepare custom pricing proposal',
    leadId: '2',
    leadName: 'Michael Chen',
    assignedTo: 'Jane Smith',
    priority: 'medium',
    status: 'in-progress',
    dueDate: '2025-01-20',
    createdAt: '2025-01-14'
  },
  {
    id: '3',
    title: 'Contract review with Emily',
    description: 'Review and finalize contract terms',
    leadId: '3',
    leadName: 'Emily Rodriguez',
    assignedTo: 'John Doe',
    priority: 'high',
    status: 'pending',
    dueDate: '2025-01-17',
    createdAt: '2025-01-13'
  }
];

const Index = () => {
  const [leads, setLeads] = useState<Lead[]>(mockLeads);
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isAddLeadOpen, setIsAddLeadOpen] = useState(false);
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState<string>('');
  const [activeView, setActiveView] = useState<'dashboard' | 'leads' | 'tasks'>('dashboard');

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lead.company.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const upcomingTasks = tasks.filter(task => task.status !== 'completed').slice(0, 5);
  const totalValue = leads.reduce((sum, lead) => sum + lead.value, 0);

  const addLead = (newLead: Omit<Lead, 'id' | 'createdAt'>) => {
    const lead: Lead = {
      ...newLead,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0]
    };
    setLeads([lead, ...leads]);
  };

  const addTask = (newTask: Omit<Task, 'id' | 'createdAt'>) => {
    const task: Task = {
      ...newTask,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0]
    };
    setTasks([task, ...tasks]);
  };

  const updateTaskStatus = (taskId: string, newStatus: Task['status']) => {
    setTasks(tasks.map(task => 
      task.id === taskId ? { ...task, status: newStatus } : task
    ));
  };

  const handleAddTaskToLead = (leadId: string) => {
    setSelectedLeadId(leadId);
    setIsAddTaskOpen(true);
  };

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{leads.length}</div>
            <p className="text-xs text-muted-foreground">Active prospects</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            <div className="text-green-600">$</div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total potential revenue</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Tasks</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{upcomingTasks.length}</div>
            <p className="text-xs text-muted-foreground">Pending actions</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Recent Leads
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {leads.slice(0, 3).map(lead => (
              <div key={lead.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <Avatar>
                    <AvatarFallback>{lead.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-medium">{lead.name}</p>
                    <p className="text-sm text-muted-foreground">{lead.company}</p>
                  </div>
                </div>
                <Badge variant={lead.status === 'new' ? 'default' : 
                              lead.status === 'contacted' ? 'secondary' :
                              lead.status === 'in-progress' ? 'outline' : 'destructive'}>
                  {lead.status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckSquare className="h-5 w-5" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {upcomingTasks.map(task => (
              <div key={task.id} className="flex items-center gap-3 p-3 border rounded-lg">
                <div className="flex-1">
                  <p className="font-medium">{task.title}</p>
                  <p className="text-sm text-muted-foreground">{task.leadName}</p>
                </div>
                <div className="text-right">
                  <Badge variant={task.priority === 'high' ? 'destructive' : 
                                task.priority === 'medium' ? 'default' : 'secondary'}>
                    {task.priority}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">{task.dueDate}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderLeads = () => (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Leads</h1>
          <p className="text-muted-foreground">Manage your sales prospects</p>
        </div>
        <Button onClick={() => setIsAddLeadOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Lead
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search leads..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <select
          value={statusFilter}
          onChange={(e) => setStatusFilter(e.target.value)}
          className="px-3 py-2 border rounded-md bg-background"
        >
          <option value="all">All Status</option>
          <option value="new">New</option>
          <option value="contacted">Contacted</option>
          <option value="in-progress">In Progress</option>
          <option value="closed">Closed</option>
        </select>
      </div>

      {/* Leads Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredLeads.map(lead => (
          <LeadCard key={lead.id} lead={lead} onAddTask={handleAddTaskToLead} />
        ))}
      </div>
    </div>
  );

  const renderTasks = () => (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Tasks</h1>
          <p className="text-muted-foreground">Track your follow-ups and actions</p>
        </div>
        <Button onClick={() => setIsAddTaskOpen(true)} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Task
        </Button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {tasks.map(task => (
          <TaskCard key={task.id} task={task} onStatusChange={updateTaskStatus} />
        ))}
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar activeView={activeView} onViewChange={setActiveView} />
      
      <div className="lg:ml-64 pt-16 lg:pt-0 p-4 lg:p-6">
        {activeView === 'dashboard' && renderDashboard()}
        {activeView === 'leads' && renderLeads()}
        {activeView === 'tasks' && renderTasks()}
      </div>

      <AddLeadDialog 
        open={isAddLeadOpen} 
        onOpenChange={setIsAddLeadOpen}
        onAddLead={addLead}
      />

      <AddTaskDialog 
        open={isAddTaskOpen} 
        onOpenChange={setIsAddTaskOpen}
        onAddTask={addTask}
        leads={leads}
        selectedLeadId={selectedLeadId}
      />
    </div>
  );
};

export default Index;
