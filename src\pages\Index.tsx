
import { useState } from "react";
import { Plus, Search, Filter, Users, CheckSquare, FileText, X } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import Sidebar from "@/components/Sidebar";
import AddLeadDialog from "@/components/AddLeadDialog";
import AddTaskDialog from "@/components/AddTaskDialog";
import LeadCard from "@/components/LeadCard";
import TaskCard from "@/components/TaskCard";

interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  status: 'new' | 'contacted' | 'in-progress' | 'closed';
  tags: string[];
  createdAt: string;
  assignedTo: string;
  value: number;
}

interface Task {
  id: string;
  title: string;
  description: string;
  leadId: string;
  leadName: string;
  assignedTo: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  dueDate: string;
  createdAt: string;
}

const mockLeads: Lead[] = [
  {
    id: '1',
    name: 'Sarah <PERSON>',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'TechCorp Solutions',
    status: 'new',
    tags: ['enterprise', 'priority'],
    createdAt: '2025-01-15',
    assignedTo: 'John Doe',
    value: 15000
  },
  {
    id: '2',
    name: 'Michael Chen',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Innovate Co',
    status: 'contacted',
    tags: ['startup', 'tech'],
    createdAt: '2025-01-14',
    assignedTo: 'Jane Smith',
    value: 8500
  },
  {
    id: '3',
    name: 'Emily Rodriguez',
    email: '<EMAIL>',
    phone: '+****************',
    company: 'Growth.io',
    status: 'in-progress',
    tags: ['saas', 'recurring'],
    createdAt: '2025-01-13',
    assignedTo: 'John Doe',
    value: 25000
  }
];

const mockTasks: Task[] = [
  {
    id: '1',
    title: 'Follow up call with Sarah',
    description: 'Schedule product demo for next week',
    leadId: '1',
    leadName: 'Sarah Johnson',
    assignedTo: 'John Doe',
    priority: 'high',
    status: 'pending',
    dueDate: '2025-01-18',
    createdAt: '2025-01-15'
  },
  {
    id: '2',
    title: 'Send proposal to Michael',
    description: 'Prepare custom pricing proposal',
    leadId: '2',
    leadName: 'Michael Chen',
    assignedTo: 'Jane Smith',
    priority: 'medium',
    status: 'in-progress',
    dueDate: '2025-01-20',
    createdAt: '2025-01-14'
  },
  {
    id: '3',
    title: 'Contract review with Emily',
    description: 'Review and finalize contract terms',
    leadId: '3',
    leadName: 'Emily Rodriguez',
    assignedTo: 'John Doe',
    priority: 'high',
    status: 'pending',
    dueDate: '2025-01-17',
    createdAt: '2025-01-13'
  }
];

const Index = () => {
  const [leads, setLeads] = useState<Lead[]>(mockLeads);
  const [tasks, setTasks] = useState<Task[]>(mockTasks);
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isAddLeadOpen, setIsAddLeadOpen] = useState(false);
  const [isAddTaskOpen, setIsAddTaskOpen] = useState(false);
  const [selectedLeadId, setSelectedLeadId] = useState<string>('');
  const [activeView, setActiveView] = useState<'dashboard' | 'leads' | 'tasks'>('dashboard');
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  const filteredLeads = leads.filter(lead => {
    const matchesSearch = lead.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lead.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         lead.company.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesStatus = statusFilter === 'all' || lead.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const upcomingTasks = tasks.filter(task => task.status !== 'completed').slice(0, 5);
  const totalValue = leads.reduce((sum, lead) => sum + lead.value, 0);

  const addLead = (newLead: Omit<Lead, 'id' | 'createdAt'>) => {
    const lead: Lead = {
      ...newLead,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0]
    };
    setLeads([lead, ...leads]);
  };

  const addTask = (newTask: Omit<Task, 'id' | 'createdAt'>) => {
    const task: Task = {
      ...newTask,
      id: Date.now().toString(),
      createdAt: new Date().toISOString().split('T')[0]
    };
    setTasks([task, ...tasks]);
  };

  const updateTaskStatus = (taskId: string, newStatus: Task['status']) => {
    setTasks(tasks.map(task => 
      task.id === taskId ? { ...task, status: newStatus } : task
    ));
  };

  const handleAddTaskToLead = (leadId: string) => {
    setSelectedLeadId(leadId);
    setIsAddTaskOpen(true);
  };

  const renderDashboard = () => (
    <div className="space-y-4 sm:space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
            <CardTitle className="text-sm font-medium">Total Leads</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
            <div className="text-xl sm:text-2xl font-bold">{leads.length}</div>
            <p className="text-xs text-muted-foreground">Active prospects</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
            <CardTitle className="text-sm font-medium">Pipeline Value</CardTitle>
            <div className="text-green-600">$</div>
          </CardHeader>
          <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
            <div className="text-xl sm:text-2xl font-bold">${totalValue.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">Total potential revenue</p>
          </CardContent>
        </Card>

        <Card className="sm:col-span-2 lg:col-span-1">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 px-3 sm:px-6 pt-3 sm:pt-6">
            <CardTitle className="text-sm font-medium">Active Tasks</CardTitle>
            <CheckSquare className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent className="px-3 sm:px-6 pb-3 sm:pb-6">
            <div className="text-xl sm:text-2xl font-bold">{upcomingTasks.length}</div>
            <p className="text-xs text-muted-foreground">Pending actions</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
        <Card>
          <CardHeader className="px-3 sm:px-6 pt-3 sm:pt-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <Users className="h-4 w-4 sm:h-5 sm:w-5" />
              Recent Leads
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6 pb-3 sm:pb-6">
            {leads.slice(0, 3).map(lead => (
              <div
                key={lead.id}
                className="flex items-center justify-between p-2 sm:p-3 border rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer"
                onClick={() => setActiveView('leads')}
              >
                <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
                  <Avatar className="h-8 w-8 sm:h-10 sm:w-10">
                    <AvatarFallback className="text-xs sm:text-sm">{lead.name.split(' ').map(n => n[0]).join('')}</AvatarFallback>
                  </Avatar>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-sm sm:text-base truncate">{lead.name}</p>
                    <p className="text-xs sm:text-sm text-muted-foreground truncate">{lead.company}</p>
                  </div>
                </div>
                <Badge
                  className="text-xs shrink-0 ml-2"
                  variant={lead.status === 'new' ? 'default' :
                          lead.status === 'contacted' ? 'secondary' :
                          lead.status === 'in-progress' ? 'outline' : 'destructive'}>
                  {lead.status}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="px-3 sm:px-6 pt-3 sm:pt-6">
            <CardTitle className="flex items-center gap-2 text-base sm:text-lg">
              <CheckSquare className="h-4 w-4 sm:h-5 sm:w-5" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6 pb-3 sm:pb-6">
            {upcomingTasks.map(task => (
              <div
                key={task.id}
                className="flex items-center gap-2 sm:gap-3 p-2 sm:p-3 border rounded-lg hover:bg-gray-50 active:bg-gray-100 transition-colors cursor-pointer"
                onClick={() => setActiveView('tasks')}
              >
                <div className="flex-1 min-w-0">
                  <p className="font-medium text-sm sm:text-base truncate">{task.title}</p>
                  <p className="text-xs sm:text-sm text-muted-foreground truncate">{task.leadName}</p>
                </div>
                <div className="text-right shrink-0">
                  <Badge
                    className="text-xs"
                    variant={task.priority === 'high' ? 'destructive' :
                            task.priority === 'medium' ? 'default' : 'secondary'}>
                    {task.priority}
                  </Badge>
                  <p className="text-xs text-muted-foreground mt-1">{task.dueDate}</p>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderLeads = () => (
    <div className="space-y-4 sm:space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold">Leads</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Manage your sales prospects</p>
        </div>
        <Button
          onClick={() => setIsAddLeadOpen(true)}
          className="flex items-center gap-2 w-full sm:w-auto h-10 sm:h-9"
          size="sm"
        >
          <Plus className="h-4 w-4" />
          Add Lead
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="space-y-3 sm:space-y-0">
        <div className="flex gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search leads..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-10"
            />
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowMobileFilters(!showMobileFilters)}
            className="h-10 px-3 sm:hidden"
            aria-label="Toggle filters"
          >
            <Filter className="h-4 w-4" />
          </Button>
        </div>

        <div className={`${showMobileFilters ? 'block' : 'hidden'} sm:block`}>
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 h-10 border rounded-md bg-background text-sm sm:text-base w-full sm:w-auto sm:min-w-[140px]"
          >
            <option value="all">All Status</option>
            <option value="new">New</option>
            <option value="contacted">Contacted</option>
            <option value="in-progress">In Progress</option>
            <option value="closed">Closed</option>
          </select>
        </div>
      </div>

      {/* Leads Grid */}
      {filteredLeads.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 lg:gap-6">
          {filteredLeads.map(lead => (
            <LeadCard key={lead.id} lead={lead} onAddTask={handleAddTaskToLead} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
          <p className="text-gray-500 mb-4">
            {searchQuery || statusFilter !== 'all'
              ? 'Try adjusting your search or filters'
              : 'Get started by adding your first lead'}
          </p>
          {!searchQuery && statusFilter === 'all' && (
            <Button onClick={() => setIsAddLeadOpen(true)} className="mx-auto">
              <Plus className="h-4 w-4 mr-2" />
              Add Your First Lead
            </Button>
          )}
        </div>
      )}
    </div>
  );

  const renderTasks = () => (
    <div className="space-y-4 sm:space-y-6">
      <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 items-start sm:items-center justify-between">
        <div>
          <h1 className="text-xl sm:text-2xl font-bold">Tasks</h1>
          <p className="text-sm sm:text-base text-muted-foreground">Track your follow-ups and actions</p>
        </div>
        <Button
          onClick={() => setIsAddTaskOpen(true)}
          className="flex items-center gap-2 w-full sm:w-auto h-10 sm:h-9"
          size="sm"
        >
          <Plus className="h-4 w-4" />
          Add Task
        </Button>
      </div>

      {tasks.length > 0 ? (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-3 sm:gap-4 lg:gap-6">
          {tasks.map(task => (
            <TaskCard key={task.id} task={task} onStatusChange={updateTaskStatus} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <CheckSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No tasks yet</h3>
          <p className="text-gray-500 mb-4">Create your first task to get started</p>
          <Button onClick={() => setIsAddTaskOpen(true)} className="mx-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add Your First Task
          </Button>
        </div>
      )}
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      <Sidebar activeView={activeView} onViewChange={setActiveView} />

      <div className="lg:ml-64 pt-16 lg:pt-0 p-3 sm:p-4 lg:p-6 pb-20 sm:pb-4 lg:pb-6">
        {activeView === 'dashboard' && renderDashboard()}
        {activeView === 'leads' && renderLeads()}
        {activeView === 'tasks' && renderTasks()}
      </div>

      {/* Mobile Floating Action Button */}
      <div className="fixed bottom-4 right-4 sm:hidden z-40">
        <Button
          onClick={() => activeView === 'leads' ? setIsAddLeadOpen(true) : setIsAddTaskOpen(true)}
          className="h-14 w-14 rounded-full shadow-lg hover:shadow-xl transition-shadow"
          size="sm"
        >
          <Plus className="h-6 w-6" />
        </Button>
      </div>

      <AddLeadDialog
        open={isAddLeadOpen}
        onOpenChange={setIsAddLeadOpen}
        onAddLead={addLead}
      />

      <AddTaskDialog
        open={isAddTaskOpen}
        onOpenChange={setIsAddTaskOpen}
        onAddTask={addTask}
        leads={leads}
        selectedLeadId={selectedLeadId}
      />
    </div>
  );
};

export default Index;
