
import { Mail, Phone, Building, Calendar, DollarSign, Plus } from "lucide-react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";

interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  status: 'new' | 'contacted' | 'in-progress' | 'closed';
  tags: string[];
  createdAt: string;
  assignedTo: string;
  value: number;
}

interface LeadCardProps {
  lead: Lead;
  onAddTask?: (leadId: string) => void;
}

const LeadCard = ({ lead, onAddTask }: LeadCardProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardHeader className="pb-3 px-3 sm:px-6 pt-3 sm:pt-6">
        <div className="flex items-start justify-between gap-2">
          <div className="flex items-center gap-2 sm:gap-3 min-w-0 flex-1">
            <Avatar className="h-8 w-8 sm:h-10 sm:w-10 shrink-0">
              <AvatarFallback className="bg-blue-100 text-blue-700 text-xs sm:text-sm">
                {lead.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-base sm:text-lg truncate">{lead.name}</h3>
              <p className="text-xs sm:text-sm text-gray-600 flex items-center gap-1 truncate">
                <Building className="h-3 w-3 shrink-0" />
                <span className="truncate">{lead.company}</span>
              </p>
            </div>
          </div>
          <Badge className={`${getStatusColor(lead.status)} text-xs shrink-0`}>
            {lead.status.replace('-', ' ')}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-3 sm:space-y-4 px-3 sm:px-6 pb-3 sm:pb-6">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
            <Mail className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
            <span className="truncate">{lead.email}</span>
          </div>
          <div className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
            <Phone className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
            <span className="truncate">{lead.phone}</span>
          </div>
        </div>

        <div className="flex items-center justify-between gap-2">
          <div className="flex items-center gap-1 text-xs sm:text-sm font-medium text-green-600">
            <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 shrink-0" />
            <span>${lead.value.toLocaleString()}</span>
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500 shrink-0">
            <Calendar className="h-3 w-3 shrink-0" />
            <span>{new Date(lead.createdAt).toLocaleDateString()}</span>
          </div>
        </div>

        <div className="flex flex-wrap gap-1">
          {lead.tags.map(tag => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        <div className="pt-2 border-t space-y-2">
          <p className="text-xs text-gray-500">
            Assigned to: <span className="font-medium">{lead.assignedTo}</span>
          </p>
          {onAddTask && (
            <Button
              size="sm"
              variant="outline"
              className="w-full h-8 sm:h-9 text-xs sm:text-sm"
              onClick={() => onAddTask(lead.id)}
            >
              <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
              Add Task
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LeadCard;
