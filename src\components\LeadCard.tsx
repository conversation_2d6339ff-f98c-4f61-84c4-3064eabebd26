
import { Mail, Phone, Building, Calendar, DollarSign, Plus } from "lucide-react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";

interface Lead {
  id: string;
  name: string;
  email: string;
  phone: string;
  company: string;
  status: 'new' | 'contacted' | 'in-progress' | 'closed';
  tags: string[];
  createdAt: string;
  assignedTo: string;
  value: number;
}

interface LeadCardProps {
  lead: Lead;
  onAddTask?: (leadId: string) => void;
}

const LeadCard = ({ lead, onAddTask }: LeadCardProps) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-yellow-100 text-yellow-800';
      case 'in-progress': return 'bg-green-100 text-green-800';
      case 'closed': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar>
              <AvatarFallback className="bg-blue-100 text-blue-700">
                {lead.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
            <div>
              <h3 className="font-semibold text-lg">{lead.name}</h3>
              <p className="text-sm text-gray-600 flex items-center gap-1">
                <Building className="h-3 w-3" />
                {lead.company}
              </p>
            </div>
          </div>
          <Badge className={getStatusColor(lead.status)}>
            {lead.status.replace('-', ' ')}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Mail className="h-4 w-4" />
            <span className="truncate">{lead.email}</span>
          </div>
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Phone className="h-4 w-4" />
            <span>{lead.phone}</span>
          </div>
        </div>

        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 text-sm font-medium text-green-600">
            <DollarSign className="h-4 w-4" />
            ${lead.value.toLocaleString()}
          </div>
          <div className="flex items-center gap-1 text-xs text-gray-500">
            <Calendar className="h-3 w-3" />
            {new Date(lead.createdAt).toLocaleDateString()}
          </div>
        </div>

        <div className="flex flex-wrap gap-1">
          {lead.tags.map(tag => (
            <Badge key={tag} variant="outline" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        <div className="pt-2 border-t space-y-2">
          <p className="text-xs text-gray-500">
            Assigned to: <span className="font-medium">{lead.assignedTo}</span>
          </p>
          {onAddTask && (
            <Button 
              size="sm" 
              variant="outline" 
              className="w-full"
              onClick={() => onAddTask(lead.id)}
            >
              <Plus className="h-4 w-4 mr-1" />
              Add Task
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default LeadCard;
