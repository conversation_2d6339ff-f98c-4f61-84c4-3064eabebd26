
import { Calendar, User, AlertCircle, CheckCircle, Clock } from "lucide-react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

interface Task {
  id: string;
  title: string;
  description: string;
  leadId: string;
  leadName: string;
  assignedTo: string;
  priority: 'low' | 'medium' | 'high';
  status: 'pending' | 'in-progress' | 'completed';
  dueDate: string;
  createdAt: string;
}

interface TaskCardProps {
  task: Task;
  onStatusChange: (taskId: string, newStatus: Task['status']) => void;
}

const TaskCard = ({ task, onStatusChange }: TaskCardProps) => {
  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in-progress': return <Clock className="h-4 w-4 text-blue-600" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const isOverdue = new Date(task.dueDate) < new Date() && task.status !== 'completed';

  return (
    <Card className={`hover:shadow-md transition-shadow ${isOverdue ? 'border-red-200' : ''}`}>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            {getStatusIcon(task.status)}
            <h3 className="font-semibold">{task.title}</h3>
          </div>
          <Badge className={getPriorityColor(task.priority)}>
            {task.priority}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <p className="text-sm text-gray-600">{task.description}</p>

        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <User className="h-4 w-4" />
            <span>Lead: {task.leadName}</span>
          </div>
          <div className="flex items-center gap-2 text-sm">
            <Calendar className="h-4 w-4" />
            <span className={isOverdue ? 'text-red-600 font-medium' : 'text-gray-600'}>
              Due: {new Date(task.dueDate).toLocaleDateString()}
            </span>
          </div>
        </div>

        <div className="flex gap-2 pt-2">
          {task.status === 'pending' && (
            <Button 
              size="sm" 
              variant="outline"
              onClick={() => onStatusChange(task.id, 'in-progress')}
            >
              Start Task
            </Button>
          )}
          {task.status === 'in-progress' && (
            <Button 
              size="sm" 
              onClick={() => onStatusChange(task.id, 'completed')}
            >
              Complete
            </Button>
          )}
          {task.status === 'completed' && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              Completed
            </Badge>
          )}
        </div>

        <div className="pt-2 border-t">
          <p className="text-xs text-gray-500">
            Assigned to: <span className="font-medium">{task.assignedTo}</span>
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default TaskCard;
