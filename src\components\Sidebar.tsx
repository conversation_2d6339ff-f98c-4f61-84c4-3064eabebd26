import { Home, Users, CheckSquare, <PERSON>u, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface SidebarProps {
  activeView: 'dashboard' | 'leads' | 'tasks';
  onViewChange: (view: 'dashboard' | 'leads' | 'tasks') => void;
}
const Sidebar = ({
  activeView,
  onViewChange
}: SidebarProps) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const menuItems = [{
    id: 'dashboard',
    label: 'Dashboard',
    icon: Home
  }, {
    id: 'leads',
    label: 'Leads',
    icon: Users
  }, {
    id: 'tasks',
    label: 'Tasks',
    icon: CheckSquare
  }];

  const handleMenuClick = (view: 'dashboard' | 'leads' | 'tasks') => {
    onViewChange(view);
    setIsMobileMenuOpen(false); // Close mobile menu after selection
  };

  return (
    <>
      {/* Mobile Header */}
      <div className="lg:hidden fixed top-0 left-0 right-0 bg-white border-b border-gray-200 z-50 h-16">
        <div className="flex items-center justify-between h-full px-4">
          <div>
            <h1 className="text-lg font-bold text-gray-900">Lead Management</h1>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 rounded-lg hover:bg-gray-100"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>
      </div>

      {/* Mobile Menu Overlay */}
      {isMobileMenuOpen && (
        <div 
          className="lg:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div className={cn(
        "fixed top-0 h-full bg-white border-r border-gray-200 z-50 transition-transform duration-300",
        "lg:left-0 lg:w-64 lg:translate-x-0", // Desktop: always visible
        "w-64", // Mobile width
        isMobileMenuOpen ? "left-0 translate-x-0" : "-translate-x-full", // Mobile: slide in/out
        "lg:top-0", // Desktop: full height
        "top-16 lg:top-0" // Mobile: below header
      )}>
        {/* Desktop Header */}
        <div className="hidden lg:block p-6 border-b">
          <h1 className="text-xl font-bold text-gray-900">Lead Management</h1>
          <p className="text-sm text-gray-500">CRM System</p>
        </div>

        <nav className="p-4 space-y-2">
          {menuItems.map(item => {
            const Icon = item.icon;
            const isActive = activeView === item.id;
            return (
              <button 
                key={item.id} 
                onClick={() => handleMenuClick(item.id as any)} 
                className={cn(
                  "w-full flex items-center gap-3 px-4 py-3 text-left rounded-lg transition-colors",
                  isActive 
                    ? "bg-blue-50 text-blue-700 font-medium" 
                    : "text-gray-600 hover:bg-gray-50 hover:text-gray-900"
                )}
              >
                <Icon className="h-5 w-5" />
                {item.label}
              </button>
            );
          })}
        </nav>

        <div className="absolute bottom-4 left-4 right-4">
          <div className="p-4 bg-gray-50 rounded-lg">
            <p className="text-sm font-medium text-gray-900">Team Workspace</p>
            <p className="text-xs text-gray-500">3 members active</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default Sidebar;